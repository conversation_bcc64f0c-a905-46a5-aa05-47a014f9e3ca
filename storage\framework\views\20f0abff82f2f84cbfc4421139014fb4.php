<?php $__env->startSection('title', 'Đặt vé xem phim'); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/dat-ve.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="dat-ve-container">
    <div class="container">
        <!-- Thông tin phim -->
        <div class="movie-info">
            <div class="movie-poster">
                <img src="<?php echo e(asset('storage/' . $suatChieu->phim->poster)); ?>" alt="<?php echo e($suatChieu->phim->ten_phim); ?>">
            </div>
            <div class="movie-details">
                <h2><?php echo e($suatChieu->phim->ten_phim); ?></h2>
                <div class="movie-meta">
                    <p><i class="fas fa-clock"></i> <?php echo e($suatChieu->phim->thoi_luong); ?> phút</p>
                    <p><i class="fas fa-calendar"></i> <?php echo e(\Carbon\Carbon::parse($suatChieu->ngay_chieu)->format('d/m/Y')); ?></p>
                    <p><i class="fas fa-clock"></i> <?php echo e(\Carbon\Carbon::parse($suatChieu->bat_dau)->format('H:i')); ?> - <?php echo e(\Carbon\Carbon::parse($suatChieu->ket_thuc)->format('H:i')); ?></p>
                    <p><i class="fas fa-film"></i> <?php echo e($suatChieu->phien_ban_phim); ?></p>
                </div>
                <div class="cinema-info">
                    <p><i class="fas fa-map-marker-alt"></i> <?php echo e($suatChieu->phongChieu->rapPhim->chiNhanh->ten_chi_nhanh); ?></p>
                    <p><i class="fas fa-door-open"></i> <?php echo e($suatChieu->phongChieu->ten_phong); ?></p>
                </div>
            </div>
        </div>

        <!-- Chọn ghế -->
        <div class="seat-selection">
            <h3>Chọn ghế ngồi</h3>
            <div class="screen">
                <div class="screen-text">MÀN HÌNH</div>
            </div>
            
            <div class="seat-map" id="seat-map">
                <?php
                    $currentRow = '';
                    $seatsByRow = $gheNgois->groupBy('hang_ghe');
                ?>
                
                <?php $__currentLoopData = $seatsByRow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hangGhe => $ghes): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="seat-row">
                        <div class="row-label"><?php echo e($hangGhe); ?></div>
                        <div class="seats">
                            <?php $__currentLoopData = $ghes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ghe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="seat <?php echo e($ghe->da_dat ? 'occupied' : 'available'); ?> <?php echo e($ghe->loaiGhe->ten_loai_ghe); ?>" 
                                     data-seat-id="<?php echo e($ghe->id); ?>"
                                     data-seat-name="<?php echo e($hangGhe); ?><?php echo e($ghe->so_ghe); ?>"
                                     data-seat-price="<?php echo e($ghe->loaiGhe->gia); ?>"
                                     <?php if($ghe->da_dat): ?> disabled <?php endif; ?>>
                                    <?php echo e($ghe->so_ghe); ?>

                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="row-label"><?php echo e($hangGhe); ?></div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Chú thích ghế -->
            <div class="seat-legend">
                <div class="legend-item">
                    <div class="seat available"></div>
                    <span>Ghế trống</span>
                </div>
                <div class="legend-item">
                    <div class="seat selected"></div>
                    <span>Ghế đã chọn</span>
                </div>
                <div class="legend-item">
                    <div class="seat occupied"></div>
                    <span>Ghế đã đặt</span>
                </div>
                <div class="legend-item">
                    <div class="seat vip"></div>
                    <span>Ghế VIP</span>
                </div>
            </div>
        </div>

        <!-- Chọn đồ ăn -->
        <div class="food-selection">
            <h3>Chọn đồ ăn & nước uống</h3>
            <div class="food-tabs">
                <button class="tab-btn active" data-tab="do-an">Đồ ăn</button>
                <button class="tab-btn" data-tab="combo">Combo</button>
            </div>

            <!-- Tab đồ ăn -->
            <div class="tab-content active" id="do-an">
                <div class="food-grid">
                    <?php $__currentLoopData = $doAns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $doAn): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="food-item">
                            <img src="<?php echo e(asset('storage/' . $doAn->hinh_anh)); ?>" alt="<?php echo e($doAn->tieu_de); ?>">
                            <div class="food-info">
                                <h4><?php echo e($doAn->tieu_de); ?></h4>
                                <p class="price"><?php echo e(number_format($doAn->gia)); ?>đ</p>
                                <div class="quantity-control">
                                    <button type="button" class="qty-btn minus" data-target="do-an-<?php echo e($doAn->id); ?>">-</button>
                                    <input type="number" name="do_an[<?php echo e($doAn->id); ?>]" id="do-an-<?php echo e($doAn->id); ?>" value="0" min="0" max="10" readonly>
                                    <button type="button" class="qty-btn plus" data-target="do-an-<?php echo e($doAn->id); ?>">+</button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Tab combo -->
            <div class="tab-content" id="combo">
                <div class="food-grid">
                    <?php $__currentLoopData = $combos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $combo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="food-item">
                            <img src="<?php echo e(asset('storage/' . $combo->hinh_anh)); ?>" alt="<?php echo e($combo->ten_combo); ?>">
                            <div class="food-info">
                                <h4><?php echo e($combo->ten_combo); ?></h4>
                                <p class="description"><?php echo e($combo->mo_ta); ?></p>
                                <p class="price"><?php echo e(number_format($combo->gia)); ?>đ</p>
                                <div class="quantity-control">
                                    <button type="button" class="qty-btn minus" data-target="combo-<?php echo e($combo->id); ?>">-</button>
                                    <input type="number" name="combo[<?php echo e($combo->id); ?>]" id="combo-<?php echo e($combo->id); ?>" value="0" min="0" max="10" readonly>
                                    <button type="button" class="qty-btn plus" data-target="combo-<?php echo e($combo->id); ?>">+</button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Tóm tắt đơn hàng -->
        <div class="order-summary">
            <h3>Tóm tắt đơn hàng</h3>
            <div class="summary-content">
                <div class="selected-seats">
                    <h4>Ghế đã chọn:</h4>
                    <div id="selected-seats-list">Chưa chọn ghế</div>
                </div>
                <div class="selected-food">
                    <h4>Đồ ăn & nước uống:</h4>
                    <div id="selected-food-list">Chưa chọn</div>
                </div>
                <div class="total-price">
                    <h4>Tổng tiền: <span id="total-amount">0đ</span></h4>
                </div>
                <button type="button" id="btn-dat-ve" class="btn-primary" disabled>Đặt vé</button>
            </div>
        </div>
    </div>
</div>

<!-- Form ẩn để submit -->
<form id="booking-form" style="display: none;">
    <?php echo csrf_field(); ?>
    <input type="hidden" name="suat_chieu_id" value="<?php echo e($suatChieu->id); ?>">
    <div id="selected-seats-input"></div>
    <div id="selected-food-input"></div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset('js/dat-ve-client.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/client/dat-ve/index.blade.php ENDPATH**/ ?>